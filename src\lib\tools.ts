
import type { LucideIcon } from 'lucide-react';
import type { ComponentType } from 'react';
import {
  Landmark,
  ArrowRightLeft,
  CalendarClock,
  HeartPulse,
  GraduationCap,
  Code,
  Sparkles,
  Puzzle,
  Wallet,
  // Tool icons
  ClipboardList,
  Receipt,
  Gem,
  TrendingUp,
  Coins,
  Percent,
  Box,
  Calculator,
  Timer,
  Clock,
  History,
  PiggyBank,
  QrCode,
  SpellCheck,
  Ruler,
  Repeat,
  Flame,
  Baby,
  CalendarHeart,
  Cake,
  CalendarRange,
  CalendarDays,
  BookOpen,
  Users,
  Globe,
  MessageCircle,
  SquareRadical,
  Divide,
  Eraser,
  CalendarCheck,
  Moon,
  Sun,
  School,
  Coffee,
  Building,
  Weight,
  Type,
  EyeOff,
  Wand2,
  Beef,
  Route,
  Pen,
  Droplets,
  ScanLine,
  Hash,
  FileText,
  HandHeart,
  X,
  HeartCrack,
  Heart,
  Zap,
  Shield,
  Flower2,
  Cat,
  BrainCircuit,
  Eye,
  Text,
  Lightbulb,
  ImageIcon,
  LineChart,
  Wheat,
  PawPrint,
  FileImage,
} from 'lucide-react';

// Import data fetching functions
import { getCurrencyRates } from './actions/currency';
import { getIpInfo } from './actions/ip';
import { getGoldAndSilverPrices } from './actions/gold';
import { getAramcoStock } from './actions/aramco';
import * as DateCalculators from './dates';

export interface Tool {
  name: string;
  slug: string;
  path: string;
  description: string;
  icon?: LucideIcon;
  component?: ComponentType<any>;
  getData?: () => Promise<any>;
  seoDescription?: string;
  faq?: { question: string; answer: string }[];
  relatedSlugs?: string[];
}

export interface ToolCategory {
  name: string;
  slug: string;
  description: string;
  icon: LucideIcon;
  tools: Tool[];
}

export const toolCategories: ToolCategory[] = [
  {
    name: 'الحاسبات المالية',
    slug: 'financial-calculators',
    description: 'أدوات لإدارة أموالك وحساباتك بدقة وسهولة.',
    icon: Landmark,
    tools: [
      { 
        name: 'حساب زكاة المال', 
        slug: 'zakat-calculator', 
        path: '/tools/zakat-calculator', 
        description: 'احسب قيمة الزكاة الواجبة على أموالك.', 
        icon: Coins,
        getData: () => getGoldAndSilverPrices(),
        relatedSlugs: ['gold-price', 'currency-converter', 'percentage-calculator'],
      },
      { 
        name: 'حساب ضريبة القيمة المضافة', 
        slug: 'vat-calculator', 
        path: '/tools/vat-calculator', 
        description: 'حساب ضريبة القيمة المضافة بسهولة.', 
        icon: Receipt,
        relatedSlugs: ['percentage-calculator', 'discount-calculator'],
      },
      { 
        name: 'تحويل العملات', 
        slug: 'currency-converter', 
        path: '/tools/currency-converter', 
        description: 'أسعار صرف العملات محدثة باستمرار.', 
        getData: getCurrencyRates, 
        icon: ArrowRightLeft,
        relatedSlugs: ['gold-price', 'aramco-stock'],
      },
      { 
        name: 'أسعار الذهب اليوم', 
        slug: 'gold-price', 
        path: '/tools/gold-price', 
        description: 'اطلع على أسعار الذهب لحظة بلحظة.', 
        getData: () => getGoldAndSilverPrices(), 
        icon: Gem,
        relatedSlugs: ['ounce-to-gram-converter', 'currency-converter', 'zakat-calculator'],
      },
      { 
        name: 'سعر سهم أرامكو اليوم', 
        slug: 'aramco-stock', 
        path: '/tools/aramco-stock', 
        description: 'تابع سعر سهم شركة أرامكو السعودية.', 
        getData: getAramcoStock, 
        icon: TrendingUp,
      },
      { 
        name: 'حساب الخصم', 
        slug: 'discount-calculator', 
        path: '/tools/discount-calculator', 
        description: 'معرفة السعر النهائي بعد الخصم.', 
        icon: Percent,
        relatedSlugs: ['percentage-calculator', 'vat-calculator'],
      },
      {
        name: 'حاسبة الاستثمار',
        slug: 'investment-calculator',
        path: '/tools/investment-calculator',
        description: 'قدّر نمو استثماراتك المستقبلية مع الفائدة المركبة.',
        icon: LineChart,
        relatedSlugs: ['retirement-calculator', 'hourly-wage-calculator', 'percentage-calculator'],
      },
      {
        name: 'مولد الفواتير الإلكترونية - ZATCA',
        slug: 'zatca-invoice-generator',
        path: '/tools/zatca-invoice-generator',
        description: 'إنشاء فواتير إلكترونية متوافقة مع متطلبات هيئة الزكاة والضريبة والجمارك.',
        icon: Receipt,
        relatedSlugs: ['vat-calculator', 'qr-code-generator', 'number-to-words', 'zatca-qr-decoder'],
      },
      {
        name: 'فك تشفير QR Code - ZATCA',
        slug: 'zatca-qr-decoder',
        path: '/tools/zatca-qr-decoder',
        description: 'فك تشفير رموز QR الخاصة بالفواتير الإلكترونية المتوافقة مع معايير ZATCA.',
        icon: QrCode,
        relatedSlugs: ['zatca-invoice-generator', 'qr-code-reader', 'qr-code-generator'],
      },
      { 
        name: 'حساب CBM للشحن', 
        slug: 'cbm-calculator', 
        path: '/tools/cbm-calculator', 
        description: 'حساب حجم الشحنات التجارية بالمتر المكعب.', 
        icon: Box,
        relatedSlugs: ['unit-converter'],
      },
    ],
  },
  {
    name: 'أدوات الرواتب والأجور',
    slug: 'payroll-tools',
    description: 'حسابات متعلقة بالراتب والتقاعد والأجور.',
    icon: Wallet,
    tools: [
        {
          name: 'كم باقي على الراتب',
          slug: 'saudi-salary-countdown',
          path: '/tools/saudi-salary-countdown',
          description: 'عداد تنازلي لموعد صرف رواتب الموظفين في السعودية (يوم 27).',
          getData: async () => Promise.resolve(DateCalculators.getSalaryCountdown()),
          icon: Wallet,
        },
        { 
          name: 'حساب أجر الساعة', 
          slug: 'hourly-wage-calculator', 
          path: '/tools/hourly-wage-calculator', 
          description: 'احسب قيمة أجرك بالساعة.', 
          icon: Clock,
          relatedSlugs: ['overtime-calculator', 'retirement-calculator'],
        },
        { 
          name: 'حساب الأجر الإضافي', 
          slug: 'overtime-calculator', 
          path: '/tools/overtime-calculator', 
          description: 'معرفة قيمة العمل الإضافي.', 
          icon: History,
          relatedSlugs: ['hourly-wage-calculator'],
        },
        { 
          name: 'حاسبة التقاعد', 
          slug: 'retirement-calculator', 
          path: '/tools/retirement-calculator', 
          description: 'خطط لمستقبلك المالي بعد التقاعد.', 
          icon: PiggyBank,
          relatedSlugs: ['hourly-wage-calculator', 'investment-calculator'],
        },
        {
          name: 'عداد حساب المواطن',
          slug: 'citizen-account-countdown',
          path: '/tools/citizen-account-countdown',
          description: 'الوقت المتبقي حتى إيداع دفعة حساب المواطن القادمة.',
          getData: async () => Promise.resolve(DateCalculators.getCitizenAccount()),
          icon: Users,
        },
        {
          name: 'عداد راتب التقاعد',
          slug: 'retirement-pension-countdown',
          path: '/tools/retirement-pension-countdown',
          description: 'الوقت المتبقي حتى إيداع رواتب المتقاعدين.',
          getData: async () => Promise.resolve(DateCalculators.getRetirementPension()),
          icon: Building,
        },
        {
          name: 'عداد الدعم السكني',
          slug: 'housing-support-countdown',
          path: '/tools/housing-support-countdown',
          description: 'الوقت المتبقي حتى إيداع الدعم السكني للمستفيدين.',
          getData: async () => Promise.resolve(DateCalculators.getHousingSupport()),
          icon: Landmark,
        },
    ]
  },
  {
    name: 'المحولات',
    slug: 'converters',
    description: 'تحويل بين الوحدات والصيغ المختلفة بسهولة.',
    icon: ArrowRightLeft,
    tools: [
      { 
        name: 'تحويل الوحدات', 
        slug: 'unit-converter', 
        path: '/tools/unit-converter', 
        description: 'تحويل الطول والوزن والحرارة وغيرها.', 
        icon: Ruler,
        relatedSlugs: ['mile-kilometer-converter', 'cbm-calculator', 'ounce-to-gram-converter'],
      },
      {
        name: 'تحويل من ميل إلى كيلو',
        slug: 'mile-kilometer-converter',
        path: '/tools/mile-kilometer-converter',
        description: 'تحويل المسافات بين الميل والكيلومتر بسهولة.',
        icon: Route,
        relatedSlugs: ['unit-converter'],
      },
      {
        name: 'الفدان كم متر',
        slug: 'faddan-to-meter-converter',
        path: '/tools/faddan-to-meter-converter',
        description: 'تحويل المساحة من فدان إلى متر مربع والعكس.',
        icon: Ruler,
        relatedSlugs: ['unit-converter'],
      },
       {
        name: 'تحويل من جالون إلى لتر',
        slug: 'gallon-to-liter-converter',
        path: '/tools/gallon-to-liter-converter',
        description: 'تحويل حجم السوائل بين الجالون الأمريكي واللتر.',
        icon: Droplets,
        relatedSlugs: ['unit-converter'],
      },
      {
        name: 'تحويل من قدم إلى متر',
        slug: 'feet-to-meter-converter',
        path: '/tools/feet-to-meter-converter',
        description: 'تحويل الطول بين القدم والمتر.',
        icon: Ruler,
        relatedSlugs: ['unit-converter'],
      },
      { 
        name: 'تحويل من رطل الى كيلو', 
        slug: 'pound-to-kg-converter', 
        path: '/tools/pound-to-kg-converter', 
        description: 'تحويل الوزن بين الرطل (باوند) والكيلوجرام.', 
        icon: Weight,
        relatedSlugs: ['unit-converter'],
      },
      {
        name: 'اونصة الذهب كم جرام',
        slug: 'ounce-to-gram-converter',
        path: '/tools/ounce-to-gram-converter',
        description: 'تحويل أونصة تروي (وحدة قياس المعادن الثمينة) إلى جرام.',
        icon: Weight,
        relatedSlugs: ['gold-price', 'unit-converter'],
      },
      {
        name: 'تحويل PDF إلى JPG',
        slug: 'pdf-to-jpg',
        path: '/tools/pdf-to-jpg',
        description: 'تحويل ملفات PDF إلى صور JPG عالية الجودة مجاناً. استخراج صفحات PDF كصور منفصلة أو ملف ZIP واحد.',
        icon: FileImage,
        relatedSlugs: ['pdf-merger', 'qr-code-generator', 'background-removal'],
      },
      {
        name: 'دمج ملفات PDF',
        slug: 'pdf-merger',
        path: '/tools/pdf-merger',
        description: 'دمج عدة ملفات PDF في ملف واحد بسهولة وأمان. جميع العمليات تتم محلياً في متصفحك دون رفع الملفات.',
        icon: FileText,
        relatedSlugs: ['pdf-to-jpg', 'qr-code-generator', 'background-removal'],
      },
    ],
  },
  {
    name: 'أدوات الصحة واللياقة البدنية',
    slug: 'health-daily-life-tools',
    description: 'أدوات لمتابعة صحتك وتنظيم أمور حياتك.',
    icon: HeartPulse,
    tools: [
      { 
        name: 'حاسبة مؤشر كتلة الجسم', 
        slug: 'bmi-calculator', 
        path: '/tools/bmi-calculator', 
        description: 'معرفة ما إذا كان وزنك صحيًا.', 
        icon: HeartPulse,
        relatedSlugs: ['calories-calculator', 'protein-calculator'],
      },
      { 
        name: 'حاسبة السعرات الحرارية', 
        slug: 'calories-calculator', 
        path: '/tools/calories-calculator', 
        description: 'حساب احتياجك اليومي من السعرات.', 
        icon: Flame,
        relatedSlugs: ['bmi-calculator', 'protein-calculator', 'macronutrient-calculator'],
      },
      { 
        name: 'حاسبة البروتين', 
        slug: 'protein-calculator', 
        path: '/tools/protein-calculator', 
        description: 'احسب احتياجك اليومي من البروتين.', 
        icon: Beef,
        relatedSlugs: ['calories-calculator', 'bmi-calculator', 'macronutrient-calculator'],
      },
      {
        name: 'حاسبة البروتين والكارب والدهون',
        slug: 'macronutrient-calculator',
        path: '/tools/macronutrient-calculator',
        description: 'احسب احتياجك اليومي من المغذيات الكبرى (الماكروز).',
        icon: Wheat,
        relatedSlugs: ['calories-calculator', 'protein-calculator', 'bmi-calculator'],
      },
      { 
        name: 'حاسبة الحمل والولادة', 
        slug: 'pregnancy-calculator', 
        path: '/tools/pregnancy-calculator', 
        description: 'توقعي موعد ولادتك وتابعي حملك.', 
        icon: Baby,
        relatedSlugs: ['ovulation-calculator'],
      },
      { 
        name: 'حاسبة التبويض', 
        slug: 'ovulation-calculator', 
        path: '/tools/ovulation-calculator', 
        description: 'معرفة أيام التبويض لزيادة فرص الحمل.', 
        icon: CalendarHeart,
        relatedSlugs: ['pregnancy-calculator'],
      },
    ],
  },
  {
    name: 'التواريخ والأوقات',
    slug: 'dates-times',
    description: 'أدوات متنوعة للتعامل مع التواريخ والأوقات.',
    icon: CalendarClock,
    tools: [
      {
        name: 'تاريخ اليوم',
        slug: 'todays-date',
        path: '/tools/todays-date',
        description: 'عرض تاريخ اليوم بالتقويم الميلادي والهجري.',
        icon: CalendarCheck,
      },
      {
        name: 'عداد رمضان',
        slug: 'ramadan-countdown',
        path: '/tools/ramadan-countdown',
        description: 'الوقت المتبقي حتى بداية شهر رمضان المبارك.',
        getData: async () => Promise.resolve(DateCalculators.getRamadanCountdown()),
        icon: Moon,
      },
      {
        name: 'عداد عيد الفطر',
        slug: 'eid-alfitr-countdown',
        path: '/tools/eid-alfitr-countdown',
        description: 'الوقت المتبقي حتى أول أيام عيد الفطر السعيد.',
        getData: async () => Promise.resolve(DateCalculators.getEidAlFitr()),
        icon: Moon,
      },
      {
        name: 'عداد عيد الأضحى',
        slug: 'eid-aladha-countdown',
        path: '/tools/eid-aladha-countdown',
        description: 'الوقت المتبقي حتى أول أيام عيد الأضحى المبارك.',
        getData: async () => Promise.resolve(DateCalculators.getEidAlAdha()),
        icon: Moon,
      },
      {
        name: 'عداد يوم عرفة',
        slug: 'arafah-day-countdown',
        path: '/tools/arafah-day-countdown',
        description: 'الوقت المتبقي حتى يوم عرفة، ركن الحج الأعظم.',
        getData: async () => Promise.resolve(DateCalculators.getArafahDay()),
        icon: Sun,
      },
      {
        name: 'عداد يوم التأسيس',
        slug: 'founding-day-countdown',
        path: '/tools/founding-day-countdown',
        description: 'الوقت المتبقي حتى الاحتفال بيوم التأسيس للمملكة.',
        getData: async () => Promise.resolve(DateCalculators.getFoundingDay()),
        icon: Landmark,
      },
      {
        name: 'عداد اليوم الوطني السعودي',
        slug: 'saudi-national-day-countdown',
        path: '/tools/saudi-national-day-countdown',
        description: 'الوقت المتبقي حتى الاحتفال باليوم الوطني للمملكة.',
        getData: async () => Promise.resolve(DateCalculators.getNationalDay()),
        icon: CalendarCheck,
      },
      {
        name: 'عداد الإجازة القادمة',
        slug: 'next-vacation-countdown',
        path: '/tools/next-vacation-countdown',
        description: 'الوقت المتبقي حتى أقرب إجازة دراسية قادمة.',
        getData: async () => Promise.resolve(DateCalculators.getNextVacation()),
        icon: Coffee,
      },
       {
        name: 'كم باقي على المدرسة السعودية​',
        slug: 'study-calendar-countdown',
        path: '/tools/study-calendar-countdown',
        description: 'مواعيد بداية العام الدراسي والإجازات بناءً على التقويم الرسمي المعتمد.',
        getData: async () => Promise.resolve(DateCalculators.getSaudiCalendar()),
        icon: School,
      },
      {
        name: 'عداد بداية الشتاء',
        slug: 'winter-countdown',
        path: '/tools/winter-countdown',
        description: 'الوقت المتبقي حتى بداية فصل الشتاء فلكيًا.',
        getData: async () => Promise.resolve(DateCalculators.getWinterStart()),
        icon: Timer,
      },
       {
        name: 'عداد نهاية الصيف',
        slug: 'summer-end-countdown',
        path: '/tools/summer-end-countdown',
        description: 'الوقت المتبقي حتى نهاية فصل الصيف فلكيًا.',
        getData: async () => Promise.resolve(DateCalculators.getSummerEnd()),
        icon: Sun,
      },
      { 
        name: 'حاسبة العمر', 
        slug: 'age-calculator', 
        path: '/tools/age-calculator', 
        description: 'احسب عمرك بالسنوات والشهور والأيام.', 
        icon: Cake,
        relatedSlugs: ['age-difference-calculator', 'date-difference', 'date-converter'],
      },
      { 
        name: 'حساب الفرق بين تاريخين', 
        slug: 'date-difference', 
        path: '/tools/date-difference', 
        description: 'حساب المدة بين تاريخين مختلفين.', 
        icon: CalendarRange,
        relatedSlugs: ['age-calculator', 'age-difference-calculator', 'date-converter'],
      },
      { 
        name: 'حساب فرق العمر', 
        slug: 'age-difference-calculator', 
        path: '/tools/age-difference-calculator', 
        description: 'احسب فرق العمر بين شخصين بالسنوات والأشهر والأيام.', 
        icon: Users,
        relatedSlugs: ['age-calculator', 'date-difference'],
      },
      { 
        name: 'محول التاريخ', 
        slug: 'date-converter', 
        path: '/tools/date-converter', 
        description: 'التحويل بين التاريخ الميلادي والهجري والعكس.', 
        icon: CalendarDays,
        relatedSlugs: ['age-calculator', 'date-difference'],
      },
    ],
  },
  {
    name: 'أدوات التعليم',
    slug: 'education-tools',
    description: 'أدوات مفيدة للطلاب والباحثين في مسيرتهم الدراسية.',
    icon: GraduationCap,
    tools: [
      {
        name: 'جدول الضرب',
        slug: 'multiplication-table',
        path: '/tools/multiplication-table',
        description: 'عرض جدول الضرب بشكل تفاعلي لسهولة الحفظ.',
        icon: X,
      },
      { 
        name: 'حساب المعدل التراكمي', 
        slug: 'gpa-calculator', 
        path: '/tools/gpa-calculator', 
        description: 'احسب معدلك التراكمي بسهولة.', 
        icon: GraduationCap,
        relatedSlugs: ['weighted-grade-calculator', 'percentage-calculator'],
      },
      { 
        name: 'حساب النسبة الموزونة', 
        slug: 'weighted-grade-calculator', 
        path: '/tools/weighted-grade-calculator', 
        description: 'حساب الدرجة النهائية الموزونة للقبول الجامعي.', 
        icon: BookOpen,
        relatedSlugs: ['gpa-calculator'],
      },
      { 
        name: 'حساب معدل التوجيهي', 
        slug: 'jordanian-tawjihi-calculator', 
        path: '/tools/jordanian-tawjihi-calculator', 
        description: 'حساب معدل شهادة الثانوية العامة الأردنية.', 
        icon: GraduationCap,
        relatedSlugs: ['gpa-calculator'],
      },
      { 
        name: 'تحديد حجم العينة', 
        slug: 'sample-size-calculator', 
        path: '/tools/sample-size-calculator', 
        description: 'تحديد حجم العينة المناسب لبحثك العلمي.', 
        icon: Users,
      },
    ],
  },
  {
    name: 'أدوات النصوص والكتابة',
    slug: 'text-writing-tools',
    description: 'أدوات لمعالجة النصوص وتحسينها وإنشاء المحتوى.',
    icon: Text,
    tools: [
      { 
        name: 'ملخص النصوص العربية', 
        slug: 'summarize-arabic-text', 
        path: '/tools/summarize-arabic-text', 
        description: 'تلخيص النصوص العربية الطويلة بضغطة زر.', 
        icon: ClipboardList,
        relatedSlugs: ['paraphrase-text', 'word-count'],
      },
      { 
        name: 'أداة إعادة صياغة النص', 
        slug: 'paraphrase-text', 
        path: '/tools/paraphrase-text', 
        description: 'أعد صياغة النصوص والعبارات بأسلوب مختلف.', 
        icon: Wand2,
        relatedSlugs: ['summarize-arabic-text', 'word-count'],
      },
      { 
        name: 'مولد نماذج الاستقالة', 
        slug: 'resignation-letter-generator', 
        path: '/tools/resignation-letter-generator', 
        description: 'أنشئ خطاب استقالة احترافي في ثوانٍ.', 
        icon: FileText,
        relatedSlugs: ['financial-aid-request-generator'],
      },
      {
        name: 'مولد طلب مساعدة مالية',
        slug: 'financial-aid-request-generator',
        path: '/tools/financial-aid-request-generator',
        description: 'أنشئ معروض طلب مساعدة مالية بأنماط مختلفة.',
        icon: HandHeart,
        relatedSlugs: ['resignation-letter-generator'],
      },
      { 
        name: 'عداد الكلمات', 
        slug: 'word-count', 
        path: '/tools/word-count', 
        description: 'حساب عدد الكلمات والأحرف في نص معين.', 
        icon: Type,
        relatedSlugs: ['summarize-arabic-text', 'paraphrase-text'],
      },
      { 
        name: 'عكس النص', 
        slug: 'reverse-text', 
        path: '/tools/reverse-text', 
        description: 'للنصوص العربية في البرامج غير الداعمة.', 
        icon: Eraser,
      },
       { 
        name: 'تكرار النص', 
        slug: 'text-repeater', 
        path: '/tools/text-repeater', 
        description: 'تكرار أي نص أو كلمة لعدد معين من المرات.', 
        icon: Repeat,
      },
      { 
        name: 'مولد الحرف المخفي (نص فارغ)', 
        slug: 'invisible-character', 
        path: '/tools/invisible-character', 
        description: 'نسخ حروف غير مرئية لاستخدامها في التطبيقات.', 
        icon: EyeOff,
      },
      { 
        name: 'زخرفة اسماء', 
        slug: 'arabic-name-decorator', 
        path: '/tools/arabic-name-decorator', 
        description: 'زخرفة اسمك باللغة العربية والإنجليزية بأنماط فريدة.', 
        icon: Pen,
      },
    ]
  },
  {
    name: 'حاسبات عامة',
    slug: 'general-calculators',
    description: 'مجموعة من الحاسبات الرياضية واليومية المفيدة.',
    icon: Calculator,
    tools: [
      { 
        name: 'حاسبة النسبة المئوية', 
        slug: 'percentage-calculator', 
        path: '/tools/percentage-calculator', 
        description: 'إجراء كافة حسابات النسبة المئوية.', 
        icon: Percent,
        relatedSlugs: ['discount-calculator', 'vat-calculator', 'gpa-calculator'],
      },
      { 
        name: 'آلة حاسبة بسيطة', 
        slug: 'simple-calculator', 
        path: '/tools/simple-calculator', 
        description: 'إجراء العمليات الحسابية الأساسية.', 
        icon: Calculator,
      },
      { 
        name: 'حساب الجذر التربيعي', 
        slug: 'sqrt-calculator', 
        path: '/tools/sqrt-calculator', 
        description: 'إيجاد الجذر التربيعي لأي رقم.', 
        icon: SquareRadical,
      },
      { 
        name: 'حساب الوسط الحسابي', 
        slug: 'average-calculator', 
        path: '/tools/average-calculator', 
        description: 'حساب المتوسط الحسابي لمجموعة أرقام.', 
        icon: Divide,
      },
    ]
  },
  {
    name: 'اختبارات وتحليلات',
    slug: 'tests-and-analytics',
    description: 'مجموعة من الاختبارات الترفيهية لتحليل الشخصية.',
    icon: Puzzle,
    tools: [
      {
        name: 'اختبار حيوانك الروحي',
        slug: 'spirit-animal-test',
        path: '/tools/spirit-animal-test',
        description: 'اكتشف الحيوان الذي يعكس شخصيتك. ما هو حيوانك الروحي؟',
        icon: PawPrint,
        relatedSlugs: ['personality-strength-test', 'masculinity-test', 'femininity-test'],
      },
      {
        name: 'اختبار عمى الألوان',
        slug: 'color-blindness-test',
        path: '/tools/color-blindness-test',
        description: 'اختبر قدرتك على تمييز الألوان باستخدام لوحات إيشيهارا.',
        icon: Eye,
      },
      {
        name: 'اختبار لهجة أهل الداخلية',
        slug: 'omani-dakhiliyah-dialect-test',
        path: '/tools/omani-dakhiliyah-dialect-test',
        description: 'اكتشف مدى معرفتك بلهجة أهل الداخلية في سلطنة عُمان.',
        icon: MessageCircle,
      },
       {
        name: 'اختبار قوة الشخصية',
        slug: 'personality-strength-test',
        path: '/tools/personality-strength-test',
        description: 'اكتشف مدى صلابتك ومرونتك النفسية في مواجهة التحديات.',
        icon: Gem,
        relatedSlugs: ['masculinity-test', 'anger-test', 'femininity-test'],
      },
       {
        name: 'اختبار الغيرة',
        slug: 'jealousy-test',
        path: '/tools/jealousy-test',
        description: 'اكتشف مستوى الغيرة في شخصيتك من خلال هذا الاختبار البسيط.',
        icon: HeartCrack,
        relatedSlugs: ['love-test', 'friendship-test', 'anger-test'],
      },
      {
        name: 'اختبار العصبية',
        slug: 'anger-test',
        path: '/tools/anger-test',
        description: 'اكتشف مستوى هدوئك أو انفعالك في المواقف المختلفة.',
        icon: Zap,
        relatedSlugs: ['jealousy-test', 'masculinity-test'],
      },
      {
        name: 'اختبار الحب',
        slug: 'love-test',
        path: '/tools/love-test',
        description: 'اكتشف أسلوبك في الحب والعلاقات العاطفية.',
        icon: Heart,
        relatedSlugs: ['jealousy-test', 'friendship-test'],
      },
      {
        name: 'اختبار الصداقة',
        slug: 'friendship-test',
        path: '/tools/friendship-test',
        description: 'اكتشف أي نوع من الأصدقاء أنت وما هي نقاط قوتك.',
        icon: Users,
        relatedSlugs: ['love-test', 'jealousy-test'],
      },
      {
        name: 'اختبار الرجولة',
        slug: 'masculinity-test',
        path: '/tools/masculinity-test',
        description: 'اختبر جوانب القوة والمسؤولية والنضج في شخصيتك.',
        icon: Shield,
        relatedSlugs: ['anger-test', 'friendship-test'],
      },
       {
        name: 'اختبار الأنوثة',
        slug: 'femininity-test',
        path: '/tools/femininity-test',
        description: 'استكشفي جوانب شخصيتك وقوتك الداخلية والناعمة.',
        icon: Flower2,
        relatedSlugs: ['love-test', 'friendship-test'],
      },
       {
        name: 'اختبار الدلع (للدلوعات)',
        slug: 'spoiled-test',
        path: '/tools/spoiled-test',
        description: 'اكتشفي درجة الدلع في شخصيتك مع اختبار الدلوعات المرح.',
        icon: Cat,
        relatedSlugs: ['femininity-test', 'love-test'],
      },
       {
        name: 'اختبار الحساسية',
        slug: 'sensitivity-test',
        path: '/tools/sensitivity-test',
        description: 'اكتشف مدى حساسيتك وتأثرك بالبيئة والمشاعر من حولك.',
        icon: Droplets,
      },
    ]
  },
  {
    name: 'أدوات المطورين',
    slug: 'developer-tools',
    description: 'أدوات تقنية للمطورين والمصممين.',
    icon: Code,
    tools: [
      { 
        name: 'مولد رمز QR', 
        slug: 'qr-code-generator', 
        path: '/tools/qr-code-generator', 
        description: 'إنشاء رموز QR مخصصة للروابط والنصوص وغيرها.', 
        icon: QrCode,
        relatedSlugs: ['qr-code-reader', 'my-ip', 'whatsapp-tools'],
      },
       { 
        name: 'قارئ رمز QR', 
        slug: 'qr-code-reader', 
        path: '/tools/qr-code-reader', 
        description: 'فك تشفير رموز QR من خلال رفع صورة.', 
        icon: ScanLine,
        relatedSlugs: ['qr-code-generator'],
      },
      {
        name: 'معرفة IP الخاص بي',
        slug: 'my-ip',
        path: '/tools/my-ip',
        description: 'عرض عنوان IP العام الخاص بك.',
        getData: getIpInfo,
        icon: Globe,
      },
      {
        name: 'إزالة خلفية الصور',
        slug: 'background-removal',
        path: '/tools/background-removal',
        description: 'قم بإزالة خلفية الصور تلقائياً باستخدام الذكاء الاصطناعي.',
        icon: ImageIcon,
        seoDescription: 'أداة مجانية لإزالة خلفية الصور تلقائياً باستخدام الذكاء الاصطناعي. يدعم صيغ JPG، PNG، وWebP مع معالجة محلية آمنة.',
        faq: [
          {
            question: 'ما هي صيغ الصور المدعومة؟',
            answer: 'تدعم الأداة جميع صيغ الصور الشائعة مثل JPG، PNG، WebP، وGIF.'
          },
          {
            question: 'هل يتم رفع صوري إلى الخادم؟',
            answer: 'لا، تتم معالجة جميع الصور محلياً في متصفحك. لا يتم إرسال أي صور إلى خوادمنا.'
          },
          {
            question: 'ما هو الحد الأقصى لحجم الصورة؟',
            answer: 'الحد الأقصى لحجم الصورة هو 10 ميجابايت.'
          },
          {
            question: 'كيف يمكنني تحسين جودة إزالة الخلفية؟',
            answer: 'يمكنك ضبط حساسية إزالة الخلفية باستخدام شريط التمرير وإعادة المعالجة للحصول على أفضل النتائج.'
          }
        ],
        relatedSlugs: ['qr-code-generator', 'qr-code-reader'],
      },
    ]
  },
  {
    name: 'أدوات إسلامية',
    slug: 'islamic-tools',
    description: 'أدوات وحاسبات إسلامية متنوعة.',
    icon: Moon,
    tools: [
      {
        name: 'دعاء الاستخارة',
        slug: 'istikhara-prayer',
        path: '/tools/istikhara-prayer',
        description: 'دليل شامل لكيفية أداء صلاة الاستخارة ودعائها.',
        icon: BookOpen,
      },
      {
        name: 'اسئلة دينية',
        slug: 'islamic-quiz',
        path: '/tools/islamic-quiz',
        description: 'اختبر معلوماتك في القرآن والسيرة والفقه والعقيدة.',
        icon: BrainCircuit,
      },
    ]
  },
  {
    name: 'أدوات ترفيهية',
    slug: 'fun-tools',
    description: 'أدوات مسلية لقضاء وقت ممتع.',
    icon: Sparkles,
    tools: [
       {
        name: 'لعبة أسئلة للأطفال',
        slug: 'kids-quiz',
        path: '/tools/kids-quiz',
        description: 'اختبر معلوماتك العامة في العلوم والجغرافيا والحيوانات.',
        icon: Lightbulb,
      },
       { 
        name: 'حاسبة الأبراج', 
        slug: 'zodiac-sign-calculator', 
        path: '/tools/zodiac-sign-calculator', 
        description: 'اكتشف برجك الشمسي بناءً على تاريخ ميلادك.', 
        icon: Sparkles,
      },
       {
        name: 'تفقيط الأرقام',
        slug: 'number-to-words',
        path: '/tools/number-to-words',
        description: 'تحويل الأرقام إلى كلمات (تفقيط) للعملات والشيكات.',
        icon: SpellCheck,
        relatedSlugs: ['zakat-calculator'],
      },
      { 
        name: 'انشاء رابط WhatsApp', 
        slug: 'whatsapp-tools', 
        path: '/tools/whatsapp-tools', 
        description: 'حوّل رقم الواتس إلى رابط مباشر، أرسل رسائل بدون حفظ الرقم، واصنع رابط واتس اب بسهولة.', 
        getData: getIpInfo, 
        icon: MessageCircle,
      },
    ]
  },
];
